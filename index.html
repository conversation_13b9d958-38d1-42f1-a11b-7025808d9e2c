<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
    <title>Ethos Prompt</title>
    
    <!-- Performance optimizations for mobile -->
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Font preloading for better mobile performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    
    <!-- Theme Colors -->
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#000000" />
    
    <!-- For Safari 15+ -->
    <meta name="supported-color-schemes" content="light dark">
    
    <!-- Mobile-specific optimizations -->
    <meta name="apple-mobile-web-app-title" content="EthosPrompt">
    <meta name="application-name" content="EthosPrompt">
  </head>

  <body>
    <div id="root"></div>

    <script type="module" src="/client/App.tsx"></script>
  </body>
</html>
